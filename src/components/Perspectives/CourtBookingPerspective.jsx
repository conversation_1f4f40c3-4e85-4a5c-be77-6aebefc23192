export default function CourtBookingPerspective({ club }) {
  const courtDescription = club?.court_description
    ? JSON.parse(club?.court_description)
    : {
        reservation_description: "",
        payment_description: "",
      };

  return (
    <div className="space-y-6">
      <div className="rounded-lg bg-blue-50 p-4">
        <h3 className="mb-2 text-lg font-semibold text-blue-900">
          Court Booking Process
        </h3>
        <p className="text-blue-800">
          Users can reserve courts by selecting their sport, date, time,
          and players. The system supports advance booking limits, court
          selection, and find-a-buddy features.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">
            Step 1: Select Date & Time
          </h4>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>Choose sport, type, and subtype</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>Select available date within booking limits</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>Pick time slots and court (if enabled)</span>
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">
            Step 2: Add Players
          </h4>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Select players from club members</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Add family members if available</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Enable find-a-buddy for additional players</span>
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-lg bg-white p-4 shadow-sm">
        <h4 className="mb-3 font-medium text-gray-900">
          Reservation Details
        </h4>
        <div className="space-y-2 text-sm text-gray-600">
          <p>• 15-minute payment window after reservation</p>
          <p>• Automatic court assignment or user selection</p>
          <p>• Service fees and club fees calculated automatically</p>
          <p>• Email confirmations and calendar integration</p>
        </div>
      </div>

      {courtDescription.reservation_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Reservation Message
          </h4>
          <p className="text-sm text-gray-700">
            {courtDescription.reservation_description}
          </p>
        </div>
      )}

      {courtDescription.payment_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Payment Message
          </h4>
          <p className="text-sm text-gray-700">
            {courtDescription.payment_description}
          </p>
        </div>
      )}
    </div>
  );
}
