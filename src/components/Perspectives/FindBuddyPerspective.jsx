export default function FindBuddyPerspective() {
  return (
    <div className="space-y-6">
      <div className="rounded-lg bg-orange-50 p-4">
        <h3 className="mb-2 text-lg font-semibold text-orange-900">
          Find a Buddy Process
        </h3>
        <p className="text-orange-800">
          Users can create requests to find playing partners with similar
          skill levels and availability. The system matches players based
          on NTRP ratings and preferences.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">
            Step 1: Create Request
          </h4>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-orange-500"></div>
              <span>Select sport, type, and subtype</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-orange-500"></div>
              <span>Choose date and time slots</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-orange-500"></div>
              <span>Set NTRP skill level range</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-orange-500"></div>
              <span>Specify number of players needed</span>
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">
            Step 2: Player Matching
          </h4>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Request appears in open requests</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Other players can join the request</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Automatic skill level filtering</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Real-time request updates</span>
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-lg bg-white p-4 shadow-sm">
        <h4 className="mb-3 font-medium text-gray-900">
          Request Features
        </h4>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2 text-sm text-gray-600">
            <p>• NTRP skill level matching (min/max range)</p>
            <p>• Multiple time slot support</p>
            <p>• Player count flexibility</p>
            <p>• Custom notes and requirements</p>
          </div>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Advance booking limits based on membership</p>
            <p>• Request visibility controls</p>
            <p>• Automatic court booking integration</p>
            <p>• Email notifications for matches</p>
          </div>
        </div>
      </div>

      <div className="rounded-lg bg-blue-50 p-4">
        <h4 className="mb-2 font-medium text-blue-900">How It Works</h4>
        <div className="space-y-2 text-sm text-blue-800">
          <p>
            1. Create a buddy request with your preferred date, time, and
            skill level
          </p>
          <p>
            2. Other club members see your request in the "Open Requests"
            section
          </p>
          <p>
            3. Players with matching skill levels can join your request
          </p>
          <p>
            4. Once enough players join, the system can automatically book
            a court
          </p>
          <p>
            5. All participants receive confirmation and payment
            instructions
          </p>
        </div>
      </div>
    </div>
  );
}
