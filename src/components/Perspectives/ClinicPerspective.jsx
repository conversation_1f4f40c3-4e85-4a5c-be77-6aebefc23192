export default function ClinicPerspective({ club }) {
  const clinicDescription = club?.clinic_description
    ? JSON.parse(club?.clinic_description)
    : {
        reservation_description: "",
        payment_description: "",
      };

  return (
    <div className="space-y-6">
      <div className="rounded-lg bg-indigo-50 p-4">
        <h3 className="mb-2 text-lg font-semibold text-indigo-900">
          Clinic Booking Process
        </h3>
        <p className="text-indigo-800">
          Users can join group clinics led by professional coaches.
          Clinics are pre-scheduled events with fixed dates, times, and
          participant limits.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">
            Clinic Discovery
          </h4>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-indigo-500"></div>
              <span>Browse available clinics by sport</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-indigo-500"></div>
              <span>Filter by date, time, and skill level</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-indigo-500"></div>
              <span>View clinic details and coach info</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-indigo-500"></div>
              <span>Check available slots remaining</span>
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">
            Booking Process
          </h4>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Select players to register</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Review cost per participant</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Complete payment within 15 minutes</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Receive confirmation and details</span>
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-lg bg-white p-4 shadow-sm">
        <h4 className="mb-3 font-medium text-gray-900">
          Clinic Features
        </h4>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Fixed cost per participant</p>
            <p>• Limited slots per clinic</p>
            <p>• Professional coach instruction</p>
            <p>• Group learning environment</p>
          </div>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Advance booking restrictions</p>
            <p>• Multiple player registration</p>
            <p>• Automatic service fee calculation</p>
            <p>• Calendar integration</p>
          </div>
        </div>
      </div>

      <div className="rounded-lg bg-yellow-50 p-4">
        <h4 className="mb-2 font-medium text-yellow-900">Clinic Types</h4>
        <div className="space-y-2 text-sm text-yellow-800">
          <p>
            • <strong>Beginner Clinics:</strong> Introduction to basic
            techniques and rules
          </p>
          <p>
            • <strong>Intermediate Clinics:</strong> Skill development and
            strategy
          </p>
          <p>
            • <strong>Advanced Clinics:</strong> Competitive play and
            advanced techniques
          </p>
          <p>
            • <strong>Specialty Clinics:</strong> Focus on specific skills
            (serving, volleys, etc.)
          </p>
        </div>
      </div>

      {clinicDescription.reservation_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Reservation Message
          </h4>
          <p className="text-sm text-gray-700">
            {clinicDescription.reservation_description}
          </p>
        </div>
      )}

      {clinicDescription.payment_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Payment Message
          </h4>
          <p className="text-sm text-gray-700">
            {clinicDescription.payment_description}
          </p>
        </div>
      )}
    </div>
  );
}
