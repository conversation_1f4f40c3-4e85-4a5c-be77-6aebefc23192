export default function LessonBookingPerspective({ club }) {
  const lessonDescription = club?.lesson_description
    ? JSON.parse(club?.lesson_description)
    : {
        reservation_description: "",
        payment_description: "",
      };

  return (
    <div className="space-y-6">
      <div className="rounded-lg bg-purple-50 p-4">
        <h3 className="mb-2 text-lg font-semibold text-purple-900">
          Lesson Booking Process
        </h3>
        <p className="text-purple-800">
          Users can book lessons with coaches through multiple search
          methods: by coach, by time availability, or through custom
          requests.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">
            Find by Coach
          </h4>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-purple-500"></div>
              <span>Browse available coaches</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-purple-500"></div>
              <span>View coach profiles and rates</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-purple-500"></div>
              <span>Select available time slots</span>
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">Find by Time</h4>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>Choose preferred date and time</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>System finds available coaches</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>Compare options and book</span>
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm">
          <h4 className="mb-3 font-medium text-gray-900">
            Custom Request
          </h4>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Submit specific requirements</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Coaches respond to requests</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>Choose from responses</span>
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-lg bg-white p-4 shadow-sm">
        <h4 className="mb-3 font-medium text-gray-900">
          Lesson Features
        </h4>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Hourly rate-based pricing</p>
            <p>• Multiple player support</p>
            <p>• Advance booking limits</p>
          </div>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Coach profile and bio viewing</p>
            <p>• Flexible time slot selection</p>
            <p>• Automatic fee calculation</p>
          </div>
        </div>
      </div>

      {lessonDescription.reservation_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Reservation Message
          </h4>
          <p className="text-sm text-gray-700">
            {lessonDescription.reservation_description}
          </p>
        </div>
      )}

      {lessonDescription.payment_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Payment Message
          </h4>
          <p className="text-sm text-gray-700">
            {lessonDescription.payment_description}
          </p>
        </div>
      )}
    </div>
  );
}
