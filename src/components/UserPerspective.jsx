import { useState } from "react";
import BottomDrawer from "./Drawers/BottomDrawer";
import UserSignupPerspective from "./Perspectives/UserSignupPerspective";
import MembershipPerspective from "./Perspectives/MembershipPerspective";
import SplashScreenPagePreview from "./ProfileSetUp/SplashScreenPagePreview";
import SportTypeSelection from "./Shared/SportTypeSelection";
import HomeLoggedInPerspective from "./Perspectives/HomeLoggedInPerspective";
import CourtBookingPerspective from "./Perspectives/CourtBookingPerspective";
import LessonBookingPerspective from "./Perspectives/LessonBookingPerspective";
import FindBuddyPerspective from "./Perspectives/FindBuddyPerspective";
import ClinicPerspective from "./Perspectives/ClinicPerspective";

export default function UserPerspective({
  club,
  sports,
  pricing,
  courts,
  isOpen,
  onClose,
}) {
  const [activeTab, setActiveTab] = useState("Sports");

  const formatImageList = (splashScreen) => {
    try {
      // First parse the splash_screen JSON string
      const parsedSplashScreen = JSON.parse(splashScreen || "{}");
      // Get the images array from the parsed splash screen
      const images = parsedSplashScreen.images || [];

      // Create array of 9 nulls
      let formattedImages = new Array(9).fill(null);

      // Fill in the images where they exist
      images.forEach((image, index) => {
        if (image && image.url) {
          formattedImages[index] = {
            url: image.url,
            isDefault: true,
            id: image.id || `default-${index}`,
            type: image.type || "image",
          };
        }
      });

      return formattedImages;
    } catch (error) {
      console.error("Error parsing splash screen:", error);
      return new Array(9).fill(null);
    }
  };

  const handleSportSelectionChange = ({ sport, type, subType }) => {
    // Preview only - no functionality needed
    console.log({ sport, type, subType });
  };

  const menuItems = [
    { title: "Sports", path: "/sports" },
    { title: "Prices", path: "/prices" },
    { title: "Home splash screen", path: "/home-splash" },
    { title: "Home_Logged-in", path: "/home-logged-in" },
    { title: "Court booking description", path: "/court-booking" },
    { title: "Lesson booking description", path: "/lesson-booking" },
    { title: "Find a Buddy booking description", path: "/find-buddy" },
    { title: "Clinic description", path: "/clinic" },
    { title: "Custom request threshold", path: "/custom-request" },
    { title: "Membership and modules", path: "/membership" },
    { title: "Custom signup form", path: "/custom-signup-form" },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case "Sports":
        return (
          <div className="mx-auto max-w-xl">
            <SportTypeSelection
              sports={sports}
              onSelectionChange={handleSportSelectionChange}
              isChildren={false}
            />
          </div>
        );
      case "Custom signup form":
        return <UserSignupPerspective club={club} />;
      case "Membership and modules":
        return <MembershipPerspective club={club} />;
      case "Home splash screen":
        const parsedSplashScreen = JSON.parse(club?.splash_screen || "{}");
        return (
          <div className="h-full">
            <SplashScreenPagePreview
              clubName={club?.name}
              description={parsedSplashScreen?.bio}
              imageList={formatImageList(club?.splash_screen)}
              clubLogo={club?.club_logo}
              slideshowDelay={parsedSplashScreen?.slideshow_delay || 5000}
            />
          </div>
        );
      case "Home_Logged-in":
        return <HomeLoggedInPerspective club={club} />;
      case "Court booking description":
        const courtDescription = club?.court_description
          ? JSON.parse(club?.court_description)
          : {
              reservation_description: "",
              payment_description: "",
            };
        return (
          <div className="space-y-6">
            <div className="rounded-lg bg-blue-50 p-4">
              <h3 className="mb-2 text-lg font-semibold text-blue-900">
                Court Booking Process
              </h3>
              <p className="text-blue-800">
                Users can reserve courts by selecting their sport, date, time,
                and players. The system supports advance booking limits, court
                selection, and find-a-buddy features.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="rounded-lg bg-white p-4 shadow-sm">
                <h4 className="mb-3 font-medium text-gray-900">
                  Step 1: Select Date & Time
                </h4>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                    <span>Choose sport, type, and subtype</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                    <span>Select available date within booking limits</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                    <span>Pick time slots and court (if enabled)</span>
                  </div>
                </div>
              </div>

              <div className="rounded-lg bg-white p-4 shadow-sm">
                <h4 className="mb-3 font-medium text-gray-900">
                  Step 2: Add Players
                </h4>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Select players from club members</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Add family members if available</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Enable find-a-buddy for additional players</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="rounded-lg bg-white p-4 shadow-sm">
              <h4 className="mb-3 font-medium text-gray-900">
                Reservation Details
              </h4>
              <div className="space-y-2 text-sm text-gray-600">
                <p>• 15-minute payment window after reservation</p>
                <p>• Automatic court assignment or user selection</p>
                <p>• Service fees and club fees calculated automatically</p>
                <p>• Email confirmations and calendar integration</p>
              </div>
            </div>

            {courtDescription.reservation_description && (
              <div className="rounded-lg bg-gray-50 p-4">
                <h4 className="mb-2 font-medium text-gray-900">
                  Custom Reservation Message
                </h4>
                <p className="text-sm text-gray-700">
                  {courtDescription.reservation_description}
                </p>
              </div>
            )}

            {courtDescription.payment_description && (
              <div className="rounded-lg bg-gray-50 p-4">
                <h4 className="mb-2 font-medium text-gray-900">
                  Custom Payment Message
                </h4>
                <p className="text-sm text-gray-700">
                  {courtDescription.payment_description}
                </p>
              </div>
            )}
          </div>
        );
      case "Lesson booking description":
        const lessonDescription = club?.lesson_description
          ? JSON.parse(club?.lesson_description)
          : {
              reservation_description: "",
              payment_description: "",
            };
        return (
          <div className="space-y-6">
            <div className="rounded-lg bg-purple-50 p-4">
              <h3 className="mb-2 text-lg font-semibold text-purple-900">
                Lesson Booking Process
              </h3>
              <p className="text-purple-800">
                Users can book lessons with coaches through multiple search
                methods: by coach, by time availability, or through custom
                requests.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              <div className="rounded-lg bg-white p-4 shadow-sm">
                <h4 className="mb-3 font-medium text-gray-900">
                  Find by Coach
                </h4>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-purple-500"></div>
                    <span>Browse available coaches</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-purple-500"></div>
                    <span>View coach profiles and rates</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-purple-500"></div>
                    <span>Select available time slots</span>
                  </div>
                </div>
              </div>

              <div className="rounded-lg bg-white p-4 shadow-sm">
                <h4 className="mb-3 font-medium text-gray-900">Find by Time</h4>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                    <span>Choose preferred date and time</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                    <span>System finds available coaches</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                    <span>Compare options and book</span>
                  </div>
                </div>
              </div>

              <div className="rounded-lg bg-white p-4 shadow-sm">
                <h4 className="mb-3 font-medium text-gray-900">
                  Custom Request
                </h4>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Submit specific requirements</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Coaches respond to requests</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Choose from responses</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="rounded-lg bg-white p-4 shadow-sm">
              <h4 className="mb-3 font-medium text-gray-900">
                Lesson Features
              </h4>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2 text-sm text-gray-600">
                  <p>• Hourly rate-based pricing</p>
                  <p>• Multiple player support</p>
                  <p>• Advance booking limits</p>
                </div>
                <div className="space-y-2 text-sm text-gray-600">
                  <p>• Coach profile and bio viewing</p>
                  <p>• Flexible time slot selection</p>
                  <p>• Automatic fee calculation</p>
                </div>
              </div>
            </div>

            {lessonDescription.reservation_description && (
              <div className="rounded-lg bg-gray-50 p-4">
                <h4 className="mb-2 font-medium text-gray-900">
                  Custom Reservation Message
                </h4>
                <p className="text-sm text-gray-700">
                  {lessonDescription.reservation_description}
                </p>
              </div>
            )}

            {lessonDescription.payment_description && (
              <div className="rounded-lg bg-gray-50 p-4">
                <h4 className="mb-2 font-medium text-gray-900">
                  Custom Payment Message
                </h4>
                <p className="text-sm text-gray-700">
                  {lessonDescription.payment_description}
                </p>
              </div>
            )}
          </div>
        );
      case "Find a Buddy booking description":
        return (
          <div className="space-y-6">
            <div className="rounded-lg bg-orange-50 p-4">
              <h3 className="mb-2 text-lg font-semibold text-orange-900">
                Find a Buddy Process
              </h3>
              <p className="text-orange-800">
                Users can create requests to find playing partners with similar
                skill levels and availability. The system matches players based
                on NTRP ratings and preferences.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="rounded-lg bg-white p-4 shadow-sm">
                <h4 className="mb-3 font-medium text-gray-900">
                  Step 1: Create Request
                </h4>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                    <span>Select sport, type, and subtype</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                    <span>Choose date and time slots</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                    <span>Set NTRP skill level range</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                    <span>Specify number of players needed</span>
                  </div>
                </div>
              </div>

              <div className="rounded-lg bg-white p-4 shadow-sm">
                <h4 className="mb-3 font-medium text-gray-900">
                  Step 2: Player Matching
                </h4>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Request appears in open requests</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Other players can join the request</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Automatic skill level filtering</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Real-time request updates</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="rounded-lg bg-white p-4 shadow-sm">
              <h4 className="mb-3 font-medium text-gray-900">
                Request Features
              </h4>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2 text-sm text-gray-600">
                  <p>• NTRP skill level matching (min/max range)</p>
                  <p>• Multiple time slot support</p>
                  <p>• Player count flexibility</p>
                  <p>• Custom notes and requirements</p>
                </div>
                <div className="space-y-2 text-sm text-gray-600">
                  <p>• Advance booking limits based on membership</p>
                  <p>• Request visibility controls</p>
                  <p>• Automatic court booking integration</p>
                  <p>• Email notifications for matches</p>
                </div>
              </div>
            </div>

            <div className="rounded-lg bg-blue-50 p-4">
              <h4 className="mb-2 font-medium text-blue-900">How It Works</h4>
              <div className="space-y-2 text-sm text-blue-800">
                <p>
                  1. Create a buddy request with your preferred date, time, and
                  skill level
                </p>
                <p>
                  2. Other club members see your request in the "Open Requests"
                  section
                </p>
                <p>
                  3. Players with matching skill levels can join your request
                </p>
                <p>
                  4. Once enough players join, the system can automatically book
                  a court
                </p>
                <p>
                  5. All participants receive confirmation and payment
                  instructions
                </p>
              </div>
            </div>
          </div>
        );
      case "Clinic description":
        const clinicDescription = club?.clinic_description
          ? JSON.parse(club?.clinic_description)
          : {
              reservation_description: "",
              payment_description: "",
            };
        return (
          <div className="space-y-6">
            <div className="rounded-lg bg-indigo-50 p-4">
              <h3 className="mb-2 text-lg font-semibold text-indigo-900">
                Clinic Booking Process
              </h3>
              <p className="text-indigo-800">
                Users can join group clinics led by professional coaches.
                Clinics are pre-scheduled events with fixed dates, times, and
                participant limits.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="rounded-lg bg-white p-4 shadow-sm">
                <h4 className="mb-3 font-medium text-gray-900">
                  Clinic Discovery
                </h4>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-indigo-500"></div>
                    <span>Browse available clinics by sport</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-indigo-500"></div>
                    <span>Filter by date, time, and skill level</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-indigo-500"></div>
                    <span>View clinic details and coach info</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-indigo-500"></div>
                    <span>Check available slots remaining</span>
                  </div>
                </div>
              </div>

              <div className="rounded-lg bg-white p-4 shadow-sm">
                <h4 className="mb-3 font-medium text-gray-900">
                  Booking Process
                </h4>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Select players to register</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Review cost per participant</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Complete payment within 15 minutes</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500"></div>
                    <span>Receive confirmation and details</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="rounded-lg bg-white p-4 shadow-sm">
              <h4 className="mb-3 font-medium text-gray-900">
                Clinic Features
              </h4>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2 text-sm text-gray-600">
                  <p>• Fixed cost per participant</p>
                  <p>• Limited slots per clinic</p>
                  <p>• Professional coach instruction</p>
                  <p>• Group learning environment</p>
                </div>
                <div className="space-y-2 text-sm text-gray-600">
                  <p>• Advance booking restrictions</p>
                  <p>• Multiple player registration</p>
                  <p>• Automatic service fee calculation</p>
                  <p>• Calendar integration</p>
                </div>
              </div>
            </div>

            <div className="rounded-lg bg-yellow-50 p-4">
              <h4 className="mb-2 font-medium text-yellow-900">Clinic Types</h4>
              <div className="space-y-2 text-sm text-yellow-800">
                <p>
                  • <strong>Beginner Clinics:</strong> Introduction to basic
                  techniques and rules
                </p>
                <p>
                  • <strong>Intermediate Clinics:</strong> Skill development and
                  strategy
                </p>
                <p>
                  • <strong>Advanced Clinics:</strong> Competitive play and
                  advanced techniques
                </p>
                <p>
                  • <strong>Specialty Clinics:</strong> Focus on specific skills
                  (serving, volleys, etc.)
                </p>
              </div>
            </div>

            {clinicDescription.reservation_description && (
              <div className="rounded-lg bg-gray-50 p-4">
                <h4 className="mb-2 font-medium text-gray-900">
                  Custom Reservation Message
                </h4>
                <p className="text-sm text-gray-700">
                  {clinicDescription.reservation_description}
                </p>
              </div>
            )}

            {clinicDescription.payment_description && (
              <div className="rounded-lg bg-gray-50 p-4">
                <h4 className="mb-2 font-medium text-gray-900">
                  Custom Payment Message
                </h4>
                <p className="text-sm text-gray-700">
                  {clinicDescription.payment_description}
                </p>
              </div>
            )}
          </div>
        );
      default:
        return (
          <div>
            <h2 className="mb-2 text-lg font-semibold">{activeTab}</h2>
            <p className="text-gray-600">
              Content for {activeTab} will be displayed here
            </p>
          </div>
        );
    }
  };

  return (
    <BottomDrawer isOpen={isOpen} onClose={onClose} title={"Preview"}>
      <div className="flex h-full flex-col">
        <h1 className="mb-4 text-xl font-bold">{club?.name}</h1>

        {/* Scrollable Tab List */}
        <div className="relative">
          <div className="scrollbar-hide overflow-x-auto">
            <div className="flex space-x-4 border-b border-gray-200 px-4">
              {menuItems.map((item) => (
                <button
                  key={item.title}
                  onClick={() => setActiveTab(item.title)}
                  className={`whitespace-nowrap px-1 py-2 text-sm transition-colors duration-200 ${
                    activeTab === item.title
                      ? "border-b-2 border-blue-600 font-medium text-blue-600"
                      : "text-gray-500 hover:text-gray-700"
                  }`}
                >
                  {item.title}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="mt-3 flex-1 overflow-y-auto bg-white p-4">
          {renderTabContent()}
        </div>
      </div>
    </BottomDrawer>
  );
}
