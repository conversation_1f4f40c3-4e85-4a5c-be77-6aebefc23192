import React, { useState, useEffect } from "react";
import RightSideModal from "Components/RightSideModal";
import ClubSettingsEditForm from "Components/ClubSettingsEditForm";
import ExceptionEditForm from "Components/ExceptionEditForm";
import CourtEditForm from "Components/CourtEditForm";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import TimesAvailableSelector from "Components/TimesAvailableSelector";
import DeleteModal from "Components/Modals/DeleteModal";
import { actionLogTypes, activityLogTypes, logActivity } from "Utils/utils";
import LoadingSpinner from "Components/LoadingSpinner";
import HistoryComponent from "./HistoryComponent";
import ExceptionsTab from "./tabs/ExceptionsTab";
import GeneralSettingsTab from "./tabs/GeneralSettingsTab";
import CourtSettingsTab from "./tabs/CourtSettingsTab";

const sdk = new MkdSDK();

export default function CourtManagement({
  profileSettings,
  fetchSettings,
  sports = [],
  club,
  courts,
  edit_api,
}) {
  const [activeTab, setActiveTab] = useState("general-settings");
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [dropdownPosition, setDropdownPosition] = useState("bottom");
  const [showHowItWorksModal, setShowHowItWorksModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showExceptionEditModal, setShowExceptionEditModal] = useState(false);
  const [selectedException, setSelectedException] = useState(null);
  const [showAddCourtModal, setShowAddCourtModal] = useState(false);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [submitting, setSubmitting] = useState(false);
  const [addCourtLoading, setAddCourtLoading] = useState(false);
  const [showTimesAvailableModal, setShowTimesAvailableModal] = useState(false);
  const [selectedCourt, setSelectedCourt] = useState(null);
  const [selectedTimes, setSelectedTimes] = useState([]);
  const [exceptionName, setExceptionName] = useState("");
  const [isSubmittingException, setIsSubmittingException] = useState(false);
  const [showDeleteExceptionModal, setShowDeleteExceptionModal] =
    useState(false);
  const [selectedExceptionIndex, setSelectedExceptionIndex] = useState(null);
  const [deletingException, setDeletingException] = useState(false);
  const [showExceptionTimeSelector, setShowExceptionTimeSelector] =
    useState(false);
  const [selectedExceptionTimes, setSelectedExceptionTimes] = useState([]);
  const [timesSelectorIsSubmitting, setTimesSelectorIsSubmitting] =
    useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const [showEditCourtModal, setShowEditCourtModal] = useState(false);
  const [selectedCourtForEdit, setSelectedCourtForEdit] = useState(null);
  const [editCourtLoading, setEditCourtLoading] = useState(false);
  const [showTimesAvailableSelector, setShowTimesAvailableSelector] =
    useState(false);
  const [selectedCourtForTimes, setSelectedCourtForTimes] = useState(null);
  const [deleteCourtLoading, setDeleteCourtLoading] = useState(false);
  const [showDeleteCourtModal, setShowDeleteCourtModal] = useState(false);
  const [selectedCourtForDelete, setSelectedCourtForDelete] = useState(null);
  const [isLoading] = useState(false);
  const userRole = localStorage.getItem("role");
  const [exceptions, setExceptions] = useState(
    club?.exceptions ? JSON.parse(club?.exceptions) : []
  );
  const user_id = localStorage.getItem("user");
  console.log("courts", courts);
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (activeDropdown && !event.target.closest(".relative")) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [activeDropdown]);

  useEffect(() => {
    setExceptions(club?.exceptions ? JSON.parse(club?.exceptions) : []);
  }, [club?.exceptions]);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "court-management",
      },
    });
  }, []);

  const deleteException = async (index) => {
    setDeletingException(true);
    const newExceptions = exceptions.filter((_, i) => i !== index);
    try {
      await sdk.callRawAPI(edit_api, { exceptions: newExceptions }, "POST");
      await logActivity(sdk, {
        user_id,
        activity_type: activityLogTypes.court_management,
        action_type: actionLogTypes.DELETE,
        data: newExceptions,
        club_id: club?.id,
        description: "Deleted court exception",
      });

      setExceptions(newExceptions);
      setShowDeleteExceptionModal(false);
      setDeletingException(false);
      showToast(globalDispatch, "Exception deleted successfully");
    } catch (error) {
      setDeletingException(false);
    }
  };

  const handleSearchException = (value) => {
    if (value === "") {
      setExceptions(club?.exceptions ? JSON.parse(club?.exceptions) : []);
      return;
    }
    const filteredExceptions = exceptions.filter((exception) =>
      exception.name.toLowerCase().includes(value.toLowerCase())
    );
    setExceptions(filteredExceptions);
  };

  const handleDeleteCourt = async () => {
    setDeleteCourtLoading(true);
    try {
      sdk.setTable("club_court");
      await sdk.callRestAPI({ id: selectedCourtForDelete }, "DELETE");
      await logActivity(sdk, {
        user_id,
        activity_type: activityLogTypes.court_management,
        action_type: actionLogTypes.DELETE,
        data: selectedCourtForDelete,
        club_id: club?.id,
        description: "Deleted court",
      });
    } catch (error) {
      console.log(error);
    } finally {
      setDeleteCourtLoading(false);
      setSelectedCourtForDelete(null);
      setShowDeleteCourtModal(false);
      fetchSettings();
    }
  };

  console.log(exceptions);
  const renderTabContent = () => {
    switch (activeTab) {
      case "exceptions":
        return (
          <ExceptionsTab
            exceptions={exceptions}
            setExceptions={setExceptions}
            selectedException={selectedException}
            setSelectedException={setSelectedException}
            showExceptionTimeSelector={showExceptionTimeSelector}
            setShowExceptionTimeSelector={setShowExceptionTimeSelector}
            selectedExceptionTimes={selectedExceptionTimes}
            setSelectedExceptionTimes={setSelectedExceptionTimes}
            showDeleteExceptionModal={showDeleteExceptionModal}
            setShowDeleteExceptionModal={setShowDeleteExceptionModal}
            selectedExceptionIndex={selectedExceptionIndex}
            setSelectedExceptionIndex={setSelectedExceptionIndex}
            deletingException={deletingException}
            deleteException={deleteException}
            setShowExceptionEditModal={setShowExceptionEditModal}
            showHowItWorksModal={showHowItWorksModal}
            setShowHowItWorksModal={setShowHowItWorksModal}
            timesSelectorIsSubmitting={timesSelectorIsSubmitting}
            setTimesSelectorIsSubmitting={setTimesSelectorIsSubmitting}
            userRole={userRole}
            profileSettings={profileSettings}
            globalDispatch={globalDispatch}
            handleSearchException={handleSearchException}
          />
        );
      case "general-settings":
        return (
          <GeneralSettingsTab
            club={club}
            courts={courts}
            exceptions={exceptions}
            setShowEditModal={setShowEditModal}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            filteredCourts={filteredCourts}
            activeDropdown={activeDropdown}
            setActiveDropdown={setActiveDropdown}
            dropdownPosition={dropdownPosition}
            setDropdownPosition={setDropdownPosition}
            setSelectedCourtForEdit={setSelectedCourtForEdit}
            setShowEditCourtModal={setShowEditCourtModal}
            setSelectedCourtForDelete={setSelectedCourtForDelete}
            setShowDeleteCourtModal={setShowDeleteCourtModal}
            setShowAddCourtModal={setShowAddCourtModal}
            sports={sports}
            globalDispatch={globalDispatch}
            edit_api={edit_api}
            fetchSettings={fetchSettings}
          />
        );
      case "court-settings":
        return (
          <CourtSettingsTab
            courts={courts}
            sports={sports}
            edit_api={edit_api}
            globalDispatch={globalDispatch}
            fetchSettings={fetchSettings}
            club={club}
          />
        );

      default:
        return null;
    }
  };

  const filteredCourts = courts?.filter((court) => {
    const searchLower = searchQuery.toLowerCase();

    return (
      court.name?.toLowerCase().includes(searchLower) ||
      sports
        .find((s) => s.id == court.sport_id)
        ?.name?.toLowerCase()
        .includes(searchLower) ||
      court.type?.toLowerCase().includes(searchLower) ||
      court.sub_type?.toLowerCase().includes(searchLower)
    );
  });

  const HowItWorksModal = () => {
    if (!showHowItWorksModal) return null;

    return (
      <div className="fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50">
        <div className="w-full max-w-lg rounded-2xl bg-white">
          <div className="p-6">
            <div className="mb-4">
              <h2 className="text-lg font-semibold">How it works</h2>
            </div>

            <div className="mb-6 space-y-4 text-sm text-gray-600">
              <p>
                <ol className="list-inside list-decimal space-y-2">
                  <li>
                    You can define which hours the exceptions take place here
                    and the exception will automatically be added everywhere in
                    the schedule where you defined the hours for it.
                  </li>
                  <li>
                    When adding an event in the daily scheduler, the club can
                    specify these schedule exception names and define the
                    date/time/repeating details.
                  </li>
                </ol>
                <p className="mt-2">
                  Separately, you can also create custom event types in the
                  daily scheduler by using the event type "other" and defining
                  the name of the event.
                </p>
              </p>
            </div>
          </div>

          <div className="flex justify-end border-t border-gray-200 p-6">
            <button
              onClick={() => setShowHowItWorksModal(false)}
              className="rounded-xl bg-[#2B5F2B] px-5 py-3 text-sm text-white"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  };

  const formRef = React.useRef(null);

  const handleSaveSettings = async () => {
    if (formRef.current) {
      // Just trigger the submit which will handle the affected events check
      // The actual saving will be handled by the onSubmit callback passed to ClubSettingsEditForm
      await formRef.current.submit();
    }
  };

  // This function will be called by ClubSettingsEditForm when user confirms or when no conflicts
  const handleActualSave = async (formData) => {
    try {
      setSubmitting(true);
      await sdk.callRawAPI(edit_api, formData, "POST");
      await logActivity(sdk, {
        user_id,
        activity_type: activityLogTypes.court_management,
        action_type: actionLogTypes.UPDATE,
        data: formData,
        club_id: club?.id,
        description: "Updated club settings",
      });
      setShowEditModal(false);
      showToast(globalDispatch, "Settings saved successfully", 3000, "success");
      fetchSettings();
    } catch (error) {
      console.error("Error saving settings:", error);
      showToast(globalDispatch, "Error saving settings", 3000, "error");
    } finally {
      setSubmitting(false);
    }
  };

  const handleSaveException = async () => {
    setIsSubmittingException(true);
    if (!exceptionName.length) {
      showToast(globalDispatch, "Please enter a name", 3000, "error");
      return;
    }
    const newException = { name: exceptionName, days: [] };
    const newExceptions = [...exceptions, newException];
    try {
      await sdk.callRawAPI(edit_api, { exceptions: newExceptions }, "POST");
      // Log activity using the utility function
      await logActivity(sdk, {
        user_id,
        activity_type: activityLogTypes.court_management,
        action_type: actionLogTypes.CREATE,
        data: newException,
        club_id: club?.id,
        description: "Added new court exception",
      });
      setExceptions(newExceptions);
      showToast(
        globalDispatch,
        "Exception added successfully",
        3000,
        "success"
      );
      setShowExceptionEditModal(false);
      setSelectedException(newException);
      setShowExceptionTimeSelector(true);
      setExceptionName("");
    } catch (error) {
      console.error(error);
      showToast(globalDispatch, "Error adding exception", 3000, "error");
    } finally {
      setIsSubmittingException(false);
    }
  };

  const handleSaveCourt = async (courtData) => {
    setAddCourtLoading(true);
    try {
      const formData = { courts: [courtData] };
      await sdk.callRawAPI(edit_api, formData, "POST");
      await logActivity(sdk, {
        user_id,
        activity_type: activityLogTypes.court_management,
        action_type: actionLogTypes.CREATE,
        data: courtData,
        club_id: club?.id,
        description: "Added new court",
      });
      setShowAddCourtModal(false);
      showToast(globalDispatch, "Court added successfully", 3000, "success");
      fetchSettings();
    } catch (error) {
      console.error(error);
    } finally {
      setAddCourtLoading(false);
    }
  };

  const handleUpdateCourt = async (courtData) => {
    setEditCourtLoading(true);
    try {
      await sdk.callRawAPI(edit_api, { courts: [courtData] }, "POST");
      await logActivity(sdk, {
        user_id,
        activity_type: activityLogTypes.court_management,
        action_type: actionLogTypes.UPDATE,
        data: courtData,
        club_id: club?.id,
        description: "Updated court",
      });
      showToast(globalDispatch, "Court updated successfully", 3000, "success");
      setShowEditCourtModal(false);
      fetchSettings();
    } catch (error) {
      console.error(error);
      showToast(globalDispatch, "Error updating court", 3000, "error");
    } finally {
      setEditCourtLoading(false);
    }
  };

  return (
    <div className="">
      {isLoading && <LoadingSpinner />}
      <div className="flex flex-col justify-between gap-2 py-3 md:flex-row">
        <nav className="-mb-px flex space-x-8 border-b border-gray-200">
          {["General settings", "Court settings", "Exceptions"].map((tab) => (
            <button
              key={tab}
              className={`
                whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium
                ${
                  activeTab === tab.toLowerCase().replace(" ", "-")
                    ? "border-primaryBlue text-primaryBlue"
                    : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
                }
              `}
              onClick={() => setActiveTab(tab.toLowerCase().replace(" ", "-"))}
            >
              {tab}
            </button>
          ))}
        </nav>
        <HistoryComponent
          title="Court Management History"
          emptyMessage="No court management history found"
          activityType={activityLogTypes.court_management}
        />
      </div>

      {renderTabContent()}
      <HowItWorksModal />

      {/* club settings edit form  */}
      <ClubSettingsEditForm
        ref={formRef}
        club={club}
        onSubmit={handleActualSave}
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Edit club settings"
        onPrimaryAction={handleSaveSettings}
        submitting={submitting}
      />
      <RightSideModal
        isOpen={showExceptionEditModal}
        onClose={() => {
          setShowExceptionEditModal(false);
          setSelectedException(null);
        }}
        title={selectedException ? "Edit exception" : "Add exception"}
        onPrimaryAction={handleSaveException}
        primaryButtonText={selectedException ? "Save changes" : "Add exception"}
        submitting={isSubmittingException}
      >
        <ExceptionEditForm
          initialData={selectedException}
          onSubmit={handleSaveException}
          setExceptionName={setExceptionName}
          exceptionName={exceptionName}
        />
      </RightSideModal>

      <CourtEditForm
        profileSettings={profileSettings}
        club={club}
        sports={sports}
        courts={courts}
        onSubmit={handleSaveCourt}
        onClose={() => setShowAddCourtModal(false)}
        isEdit={false}
        isOpen={showAddCourtModal}
        title="Add new court"
        showFooter={false}
      />

      <TimesAvailableSelector
        selectedCourt={selectedCourt}
        selectedTimes={selectedTimes}
        setSelectedTimes={setSelectedTimes}
        showTimesAvailableModal={showTimesAvailableModal}
        setShowTimesAvailableModal={setShowTimesAvailableModal}
      />

      <CourtEditForm
        onSubmit={handleUpdateCourt}
        isEdit={true}
        initialData={selectedCourtForEdit}
        sports={sports}
        courts={courts}
        profileSettings={profileSettings}
        onClose={() => {
          setShowEditCourtModal(false);
          setSelectedCourtForEdit(null);
        }}
        isOpen={showEditCourtModal}
        title="Edit court"
        primaryButtonText="Save changes"
        submitting={editCourtLoading}
        showFooter={false}
      />

      <TimesAvailableSelector
        showTimesAvailableModal={showTimesAvailableSelector}
        setShowTimesAvailableModal={setShowTimesAvailableSelector}
        selectedCourt={selectedCourtForTimes}
        selectedTimes={selectedTimes}
        setSelectedTimes={setSelectedTimes}
        onSave={async (formattedTimes) => {
          try {
            const data = {
              courts: [
                {
                  court_id: selectedCourtForTimes.id,
                  availability: formattedTimes,
                },
              ],
            };

            await sdk.callRawAPI(edit_api, data, "POST");
            showToast(
              globalDispatch,
              "Times updated successfully",
              3000,
              "success"
            );
            fetchSettings();
          } catch (error) {
            console.error(error);
            showToast(globalDispatch, "Error updating times", 3000, "error");
          }
        }}
      />

      <DeleteModal
        isOpen={showDeleteCourtModal}
        onClose={() => {
          setShowDeleteCourtModal(false);
          setSelectedCourtForDelete(null);
        }}
        title="Delete court"
        loading={deleteCourtLoading}
        onDelete={handleDeleteCourt}
        message="Are you sure you want to delete this court?"
      />
    </div>
  );
}
