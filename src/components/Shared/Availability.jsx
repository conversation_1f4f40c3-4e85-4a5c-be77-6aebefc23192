import React, { useEffect, useState } from "react";
import { GlobalContext } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import { format } from "date-fns";
import RightSideModal from "Components/RightSideModal";
import DateRangePickerModal from "./DateRangePickerModal";
import { fCurrency } from "Utils/formatNumber";
import { convertTo12Hour } from "Utils/utils";

let sdk = new MkdSDK();

const HoursSection = ({ hours }) => (
  <div className="max-h-fit w-full rounded-xl bg-white shadow-5">
    <div className="flex items-center justify-between rounded-lg bg-gray-100 px-3 py-2">
      <p className="text-lg">Hours</p>
      <span className="text-sm text-gray-600">Schedule</span>
    </div>
    <div className="px-4 pb-4 pt-2">
      <div className="mx-3 mb-4 flex items-center gap-2 rounded-lg bg-gray-100 px-2 py-1">
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M10.0007 7.29232V10.0007L11.2507 11.2507M4.79232 16.0423H15.209C15.6692 16.0423 16.0423 15.6692 16.0423 15.209V4.79232C16.0423 4.33208 15.6692 3.95898 15.209 3.95898H4.79232C4.33208 3.95898 3.95898 4.33208 3.95898 4.79232V15.209C3.95898 15.6692 4.33208 16.0423 4.79232 16.0423ZM5.83398 3.95898L6.47738 2.02879C6.59081 1.68851 6.90926 1.45898 7.26795 1.45898H12.7334C13.092 1.45898 13.4105 1.68851 13.5239 2.02879L14.1673 3.95898H5.83398ZM5.83398 16.0423L6.47738 17.9725C6.59081 18.3128 6.90926 18.5423 7.26795 18.5423H12.7334C13.092 18.5423 13.4105 18.3128 13.5239 17.9725L14.1673 16.0423H5.83398Z"
            stroke="#868C98"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
        <span>Available</span>
        <span className="ml-auto flex items-center justify-center rounded-xl bg-primaryBlue px-3 text-sm text-white">
          {/* {hours?.total || 0} */}
        </span>
      </div>
      <div className="flex flex-col divide-y">
        {hours?.available?.length > 0 ? (
          // hours.available.map((hour) => (
          //   <div key={hour} className="flex items-center justify-between py-4">
          //     <div className="flex gap-2">
          //       <p>{convertTo12Hour(hour)}</p>
          //     </div>
          //   </div>
          // ))

          <div className="flex items-center justify-between py-4">
            <div className="flex gap-2">
              <p>
                {convertTo12Hour(hours?.range?.from)} -{" "}
                {convertTo12Hour(hours?.range?.until)}
              </p>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center py-4">
            <p className="text-sm text-gray-500">No available hours</p>
          </div>
        )}
      </div>
    </div>
  </div>
);

export default function Availability({
  clubAvailability,
  fetchClubAvailability,
  sports,
}) {
  const { dispatch: globalDispatch, state: globalState } =
    React.useContext(GlobalContext);
  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "availability",
      },
    });
  }, []);
  const [isAvailable, setIsAvailable] = useState(true);
  const [showAllCourts, setShowAllCourts] = useState(false);
  const [showAllCoaches, setShowAllCoaches] = useState(false);
  const [showAllStaff, setShowAllStaff] = useState(false);
  const [openDropdownId, setOpenDropdownId] = useState(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (openDropdownId !== null && !event.target.closest(".relative")) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [openDropdownId]);

  const [showDatePicker, setShowDatePicker] = useState(false);
  const [dateRange, setDateRange] = useState({
    start: null,
    end: null,
  });
  const [currentMonth, setCurrentMonth] = useState(new Date());

  const [selectionStart, setSelectionStart] = useState(null);
  const [selectionEnd, setSelectionEnd] = useState(null);
  const [hoverDate, setHoverDate] = useState(null);

  // Use clubAvailability data if available, otherwise fall back to props
  const courts = isAvailable
    ? clubAvailability?.courts?.available
    : clubAvailability?.courts?.unavailable;
  const staff = isAvailable
    ? clubAvailability?.staff?.available
    : clubAvailability?.staff?.unavailable;
  const coaches = isAvailable
    ? clubAvailability?.coaches?.available
    : clubAvailability?.coaches?.unavailable;
  const hours = clubAvailability?.hours;

  console.log({ clubAvailability });

  const [selectedDate, setSelectedDate] = useState(
    format(new Date(), "yyyy-MM-dd")
  );
  const [selectedTime, setSelectedTime] = useState("");

  // Handle date change
  const handleDateChange = (e) => {
    const date = e.target.value;
    setSelectedDate(date);
    fetchClubAvailability({
      date,
      time: selectedTime,
      is_available: isAvailable,
    });
  };

  // Handle time change
  const handleTimeChange = (e) => {
    const time = e.target.value;
    setSelectedTime(time);
    if (time) {
      // The time input already returns 24-hour format (HH:MM)
      // We just need to add seconds to match the API format
      fetchClubAvailability({
        date: selectedDate,
        time: `${time}:00`, // Add seconds to match API format (HH:MM:SS)
        is_available: isAvailable,
      });
    } else {
      fetchClubAvailability({
        date: selectedDate,
        is_available: isAvailable,
      });
    }
  };
  const onSportSelect = (e) => {
    const value = e.target.value;
    if (value === "") {
      fetchClubAvailability({ is_available: isAvailable });
    } else {
      fetchClubAvailability({ sport_id: value, is_available: isAvailable });
    }
  };
  return (
    <div className="flex min-h-screen flex-col gap-6 p-5">
      {/* Date and Time Picker Row */}
      <div className="flex justify-between gap-3 sm:flex-col md:flex-row">
        <div className="flex items-center gap-2">
          <input
            type="radio"
            id="available"
            checked={isAvailable}
            onChange={() => {
              setIsAvailable(true);
              fetchClubAvailability({ is_available: true });
            }}
            className="text-blue-600"
          />
          <label htmlFor="available">Available</label>

          <input
            type="radio"
            id="unavailable"
            checked={!isAvailable}
            onChange={() => {
              setIsAvailable(false);
              fetchClubAvailability({ is_available: false });
            }}
            className="ml-4 text-blue-600"
          />
          <label htmlFor="unavailable">Unavailable</label>
        </div>
        <div className="flex items-center gap-2">
          <select
            className="rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700"
            defaultValue="All"
            onChange={onSportSelect}
          >
            <option>Sport: All</option>
            {sports?.map((sport) => (
              <option value={sport.id}>{sport.name}</option>
            ))}
          </select>
          <input
            type="date"
            value={selectedDate}
            onChange={handleDateChange}
            className="rounded-lg border border-gray-300 px-3 py-1 text-gray-500 outline-gray-500"
          />
          <input
            type="time"
            value={selectedTime}
            onChange={handleTimeChange}
            className="rounded-lg border border-gray-300 px-3 py-1 text-gray-500 outline-gray-500"
          />
        </div>
      </div>

      {/* Replace the date range picker modal with the new component */}
      <DateRangePickerModal
        showDatePicker={showDatePicker}
        setShowDatePicker={setShowDatePicker}
        dateRange={dateRange}
        setDateRange={setDateRange}
        currentMonth={currentMonth}
        setCurrentMonth={setCurrentMonth}
        selectionStart={selectionStart}
        setSelectionStart={setSelectionStart}
        selectionEnd={selectionEnd}
        setSelectionEnd={setSelectionEnd}
        hoverDate={hoverDate}
        setHoverDate={setHoverDate}
      />

      {/* Grid Layout for 4 Sections */}
      <div className="flex w-full flex-col gap-6 md:w-full md:flex-row">
        {/* Courts Section */}
        <div className="max-h-fit w-full rounded-xl bg-white shadow-5">
          <div className="flex items-center justify-between rounded-lg bg-gray-100 px-3 py-2">
            <p className="text-lg">Courts</p>
            <button
              onClick={() => setShowAllCourts(true)}
              className="text-sm text-gray-600"
            >
              All courts
            </button>
          </div>
          <div className="px-4 pb-4 pt-2">
            <div className="mx-3 mb-4 flex items-center gap-2 rounded-lg bg-gray-100 px-2 py-1">
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M6.45833 3.95768V2.29102M13.5417 3.95768V2.29102M7.70833 10.6452L9.08333 12.0202L12.2917 8.81186M3.95833 16.8743H16.0417C16.5019 16.8743 16.875 16.5013 16.875 16.041V4.79102C16.875 4.33078 16.5019 3.95768 16.0417 3.95768H3.95833C3.4981 3.95768 3.125 4.33078 3.125 4.79102V16.041C3.125 16.5013 3.4981 16.8743 3.95833 16.8743Z"
                  stroke="#868C98"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <span>{isAvailable ? "Available" : "Unavailable"}</span>
              <span className="ml-auto flex items-center justify-center rounded-xl bg-primaryBlue px-3 text-sm text-white">
                {isAvailable
                  ? clubAvailability?.courts?.available?.length || 0
                  : clubAvailability?.courts?.unavailable?.length || 0}
              </span>
            </div>
            <div className="flex flex-col divide-y">
              {courts?.length > 0 ? (
                courts.map((court, index) => (
                  <div className="flex items-center justify-between py-4">
                    <div className="flex gap-2">
                      <span>{court?.name || "Court"} </span>
                      <span className="flex items-center justify-center rounded-xl bg-gray-100 px-2 text-xs text-gray-500">
                        #{court?.id}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="flex items-center justify-center py-4">
                  <p className="text-sm text-gray-500">No available courts</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Hours Section */}
        <HoursSection hours={hours} />

        {/* Coaches Section */}
        <div className="max-h-fit w-full rounded-xl bg-white shadow-5">
          <div className="flex items-center justify-between rounded-lg bg-gray-100 px-3 py-2">
            <p className="text-lg">Coaches</p>
            <button
              onClick={() => setShowAllCoaches(true)}
              className="text-sm text-gray-600"
            >
              All
            </button>
          </div>
          <div className="px-4 pb-4 pt-2">
            <div className="mx-3 mb-4 flex items-center gap-2 rounded-lg bg-gray-100 px-2 py-1">
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14.3743 5.62435V3.12435C14.3743 2.66411 14.0013 2.29102 13.541 2.29102H3.12435C2.66411 2.29102 2.29102 2.66411 2.29102 3.12435V13.541C2.29102 14.0013 2.66411 14.3743 3.12435 14.3743H5.62435M7.77124 17.7077C8.15529 15.8028 9.75317 14.3743 11.666 14.3743C13.5789 14.3743 15.1767 15.8028 15.5608 17.7077M7.77124 17.7077H6.45768C5.99745 17.7077 5.62435 17.3346 5.62435 16.8743V6.45768C5.62435 5.99745 5.99745 5.62435 6.45768 5.62435H16.8743C17.3346 5.62435 17.7077 5.99745 17.7077 6.45768V16.8743C17.7077 17.3346 17.3346 17.7077 16.8743 17.7077H15.5608M7.77124 17.7077H15.5608M13.541 10.416C13.541 11.4516 12.7016 12.291 11.666 12.291C10.6305 12.291 9.79102 11.4516 9.79102 10.416C9.79102 9.38048 10.6305 8.54102 11.666 8.54102C12.7016 8.54102 13.541 9.38048 13.541 10.416Z"
                  stroke="#868C98"
                  strokeWidth="1.5"
                  strokeLinecap="square"
                  strokeLinejoin="round"
                />
              </svg>
              <span>{isAvailable ? "Available" : "Unavailable"}</span>
              <span className="ml-auto flex items-center justify-center rounded-xl bg-primaryBlue px-3 text-sm text-white">
                {isAvailable
                  ? coaches?.length || 0
                  : clubAvailability?.coaches?.unavailable?.length || 0}
              </span>
            </div>
            <div className="flex flex-col divide-y">
              {coaches?.length > 0 ? (
                coaches.map((coach, index) => (
                  <div
                    key={index}
                    className="relative flex items-center gap-2 py-3"
                  >
                    <div className="h-8 w-8 rounded-full bg-gray-200">
                      <img
                        src={coach.photo || "/default-avatar.png"}
                        alt="coach"
                        className="h-full w-full rounded-full object-cover"
                      />
                    </div>
                    <span>
                      {coach.first_name || "N/A"} {coach.last_name || ""}
                    </span>
                    <button
                      className="ml-auto"
                      onClick={() =>
                        setOpenDropdownId(
                          openDropdownId === index ? null : index
                        )
                      }
                    >
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M7.87776 9.05723L17.5007 3.70443M7.47619 9.05227L3.20388 4.53065C2.70178 3.99926 3.07851 3.125 3.8096 3.125H17.0881C17.732 3.125 18.1326 3.82416 17.807 4.3797L10.9883 16.0142C10.6164 16.6488 9.66476 16.5255 9.46676 15.8172L7.67304 9.40025C7.63664 9.27 7.56907 9.15057 7.47619 9.05227Z"
                          stroke="#868C98"
                          strokeWidth="1.5"
                          strokeLinecap="square"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </button>

                    {/* Dropdown Menu */}
                    {openDropdownId === index && (
                      <div className="absolute right-0 top-full z-10 mt-1 w-64 rounded-lg bg-white p-4 shadow-lg">
                        <div className="mb-4">
                          <h3 className="mb-2 font-medium">
                            Contact information
                          </h3>
                          <div className="space-y-2 rounded-lg bg-gray-50 p-3">
                            <div>
                              <label className="text-sm text-gray-500">
                                EMAIL
                              </label>
                              <div className="flex items-center gap-2">
                                <span className="text-sm">
                                  {coach.email || "N/A"}
                                </span>
                                <button className="ml-auto">
                                  <svg
                                    width="20"
                                    height="20"
                                    viewBox="0 0 20 20"
                                    fill="none"
                                  >
                                    <path
                                      d="M15 6.667H8.333v1.666H15V6.667zm0 3.333H8.333v1.667H15V10zm-6.667 3.333H15V15H8.333v-1.667zM6.667 6.667H5v1.666h1.667V6.667zm0 3.333H5v1.667h1.667V10zm0 3.333H5V15h1.667v-1.667z"
                                      fill="#868C98"
                                    />
                                  </svg>
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="text-sm text-gray-500">
                                HOURLY RATE
                              </label>
                              <div className="flex items-center gap-2">
                                <span className="text-sm">
                                  {coach.hourly_rate && coach.hourly_rate > 0
                                    ? fCurrency(coach.hourly_rate)
                                    : "N/A"}
                                </span>
                              </div>
                            </div>
                            {coach.bio && (
                              <div>
                                <label className="text-sm text-gray-500">
                                  BIO
                                </label>
                                <div className="text-sm">{coach.bio}</div>
                              </div>
                            )}
                            {coach.sports?.length > 0 && (
                              <div>
                                <label className="text-sm text-gray-500">
                                  SPORTS
                                </label>
                                <div className="flex flex-wrap gap-2">
                                  {coach.sports.map((sport, idx) => (
                                    <span
                                      key={idx}
                                      className="rounded-lg bg-gray-100 px-2 py-1 text-xs text-gray-600"
                                    >
                                      {sport.sport_name} (${sport.price})
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                        <button
                          className="w-full rounded-lg bg-primaryBlue py-2 text-center text-white"
                          onClick={() => setOpenDropdownId(null)}
                        >
                          Close
                        </button>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="flex items-center justify-center py-4">
                  <p className="text-sm text-gray-500">No available coaches</p>
                </div>
              )}
            </div>
          </div>
        </div>
        {/* Staff Section */}
        <div className="max-h-fit w-full rounded-xl bg-white shadow-5">
          <div className="flex items-center justify-between rounded-lg bg-gray-100 px-3 py-2">
            <p className="text-lg">Staff working</p>
            <button
              onClick={() => setShowAllStaff(true)}
              className="text-sm text-gray-600"
            >
              All
            </button>
          </div>
          <div className="px-4 pb-4 pt-2">
            <div className="mx-3 mb-4 flex items-center gap-2 rounded-lg bg-gray-100 px-2 py-1">
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clipPath="url(#clip0_40000074_42565)">
                  <path
                    d="M12.4996 2.29102C14.1104 2.29102 15.4162 3.59685 15.4162 5.20768C15.4162 6.81851 14.1104 8.12435 12.4996 8.12435M17.2912 16.8743H18.5412C19.0015 16.8743 19.3808 16.4989 19.3088 16.0444C18.9721 13.9194 17.3725 12.0233 15.2079 11.2567M7.70789 8.12435C6.09706 8.12435 4.79123 6.81851 4.79123 5.20768C4.79123 3.59685 6.09706 2.29102 7.7079 2.29102C9.31873 2.29102 10.6246 3.59685 10.6246 5.20768C10.6246 6.81851 9.31872 8.12435 7.70789 8.12435ZM0.89718 16.0181C1.35728 13.0571 4.23121 10.6243 7.7079 10.6243C11.1846 10.6243 14.0585 13.0571 14.5186 16.0181C14.5893 16.4728 14.2098 16.8483 13.7496 16.8483H1.66623C1.20599 16.8483 0.826512 16.4728 0.89718 16.0181Z"
                    stroke="#868C98"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_40000074_42565">
                    <rect width="20" height="20" fill="white" />
                  </clipPath>
                </defs>
              </svg>

              <span>{isAvailable ? "Available" : "Unavailable"}</span>
              <span className="ml-auto flex items-center justify-center rounded-xl bg-primaryBlue px-3 text-sm text-white">
                {isAvailable
                  ? staff?.length || 0
                  : clubAvailability?.staff?.unavailable?.length || 0}
              </span>
            </div>
            <div className="flex flex-col divide-y">
              {staff?.length > 0 ? (
                staff.map((staff, index) => (
                  <div
                    key={index}
                    className="relative flex items-center gap-2 py-3"
                  >
                    <div className="h-8 w-8 rounded-full bg-gray-200">
                      <img
                        src={staff.photo || "/default-avatar.png"}
                        alt="staff"
                        className="h-full w-full rounded-full object-cover"
                      />
                    </div>
                    <span>
                      {staff.first_name} {staff.last_name}
                    </span>
                    <button
                      className="ml-auto"
                      onClick={() =>
                        setOpenDropdownId(
                          openDropdownId === index ? null : index
                        )
                      }
                    >
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M7.87776 9.05723L17.5007 3.70443M7.47619 9.05227L3.20388 4.53065C2.70178 3.99926 3.07851 3.125 3.8096 3.125H17.0881C17.732 3.125 18.1326 3.82416 17.807 4.3797L10.9883 16.0142C10.6164 16.6488 9.66476 16.5255 9.46676 15.8172L7.67304 9.40025C7.63664 9.27 7.56907 9.15057 7.47619 9.05227Z"
                          stroke="#868C98"
                          strokeWidth="1.5"
                          strokeLinecap="square"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </button>

                    {/* Dropdown Menu */}
                    {openDropdownId === index && (
                      <div className="absolute right-0 top-full z-10 mt-1 w-64 rounded-lg bg-white p-4 shadow-5">
                        <div className="mb-4">
                          <p className="mb-2 text-lg text-gray-500">
                            Contact information
                          </p>
                          <div className="space-y-2 rounded-lg bg-gray-50 p-3">
                            <div>
                              <label className="text-sm text-gray-500">
                                EMAIL
                              </label>
                              <div className="flex items-center gap-2">
                                <span className="text-sm">{staff.email}</span>
                                <button className="ml-auto">
                                  <svg
                                    width="20"
                                    height="20"
                                    viewBox="0 0 20 20"
                                    fill="none"
                                  >
                                    <path
                                      d="M15 6.667H8.333v1.666H15V6.667zm0 3.333H8.333v1.667H15V10zm-6.667 3.333H15V15H8.333v-1.667zM6.667 6.667H5v1.666h1.667V6.667zm0 3.333H5v1.667h1.667V10zm0 3.333H5V15h1.667v-1.667z"
                                      fill="#868C98"
                                    />
                                  </svg>
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="text-sm text-gray-500">
                                PHONE
                              </label>
                              <div className="flex items-center gap-2">
                                <span className="text-sm">
                                  {!staff.phone ? "N/A" : staff.phone}
                                </span>
                                <button className="ml-auto">
                                  <svg
                                    width="20"
                                    height="20"
                                    viewBox="0 0 20 20"
                                    fill="none"
                                  >
                                    <path
                                      d="M15 6.667H8.333v1.666H15V6.667zm0 3.333H8.333v1.667H15V10zm-6.667 3.333H15V15H8.333v-1.667zM6.667 6.667H5v1.666h1.667V6.667zm0 3.333H5v1.667h1.667V10zm0 3.333H5V15h1.667v-1.667z"
                                      fill="#868C98"
                                    />
                                  </svg>
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                        <button
                          className="w-full rounded-lg bg-primaryBlue py-2 text-center text-white"
                          onClick={() => setOpenDropdownId(null)}
                        >
                          Close
                        </button>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="flex items-center justify-center py-4">
                  <p className="text-sm text-gray-500">No available staff</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <RightSideModal
        isOpen={showAllCourts}
        onClose={() => setShowAllCourts(false)}
        title={"All courts"}
        showFooter={false}
      >
        <div className="space-y-6">
          <div className="mt-4">
            <div className="space-y-4">
              <div className="rounded-lg bg-gray-50 p-4">
                <p className="text-sm text-gray-600">Date and time</p>
                <span className="text-base">
                  {format(new Date(), "MMM dd, yyyy • hh:mm a")}
                </span>
              </div>
              <div className="flex flex-col gap-2 divide-y">
                {courts?.length > 0 &&
                  courts.map((court, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between pt-4"
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">
                          {court.name || `Court ${index + 1}`}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">Available</span>
                    </div>
                  ))}
                {courts?.length === 0 && (
                  <div className="flex items-center justify-center py-4">
                    <p className="text-sm text-gray-500">No available courts</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </RightSideModal>
      <RightSideModal
        isOpen={showAllStaff}
        onClose={() => setShowAllStaff(false)}
        title={"All staff"}
        showFooter={false}
      >
        <div className="space-y-6">
          <div className="mt-4">
            <div className="space-y-4">
              <div className="rounded-lg bg-gray-50 p-4">
                <p className="text-sm text-gray-600">Date and time</p>
                <span className="text-base">
                  {format(new Date(), "MMM dd, yyyy • hh:mm a")}
                </span>
              </div>
              <div className="flex flex-col gap-2 divide-y">
                {staff?.length > 0 &&
                  staff.map((staff, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between pt-4"
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">
                          {staff.first_name} {staff.last_name}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">Available</span>
                    </div>
                  ))}
                {staff?.length === 0 && (
                  <div className="flex items-center justify-center py-4">
                    <p className="text-sm text-gray-500">No available staff</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </RightSideModal>

      <RightSideModal
        isOpen={showAllCoaches}
        onClose={() => setShowAllCoaches(false)}
        title={"All coaches"}
        showFooter={false}
      >
        <div className="space-y-6">
          <div className="mt-4">
            <div className="space-y-4">
              <div className="rounded-lg bg-gray-50 p-4">
                <p className="text-sm text-gray-600">Date and time</p>
                <span className="text-base">
                  {format(new Date(), "MMM dd, yyyy • hh:mm a")}
                </span>
              </div>
              <div className="flex flex-col gap-2 divide-y">
                {coaches?.length > 0 &&
                  coaches.map((coach, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between pt-4"
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">
                          {coach.first_name} {coach.last_name}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">Available</span>
                    </div>
                  ))}
                {coaches?.length === 0 && (
                  <div className="flex items-center justify-center py-4">
                    <p className="text-sm text-gray-500">
                      No available coaches
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </RightSideModal>
    </div>
  );
}
